---
title: CrewAI
---

<Snippet file="paper-release.mdx" />

Build an AI system that combines CrewAI's agent-based architecture with Mem0's memory capabilities. This integration enables persistent memory across agent interactions and personalized task execution based on user history.

## Overview

In this guide, we'll create a CrewAI agent that:
1. Uses CrewAI to manage AI agents and tasks
2. Leverages Mem0 to store and retrieve conversation history
3. Creates personalized experiences based on stored user preferences

## Setup and Configuration

Install necessary libraries:

```bash
pip install crewai crewai-tools mem0ai
```

Import required modules and set up configurations:

<Note>Remember to get your API keys from [Mem0 Platform](https://app.mem0.ai), [OpenAI](https://platform.openai.com) and [Serper Dev](https://serper.dev) for search capabilities.</Note>

```python
import os
from mem0 import MemoryClient
from crewai import Agent, Task, Crew, Process
from crewai_tools import SerperDevTool

# Configuration
os.environ["MEM0_API_KEY"] = "your-mem0-api-key"
os.environ["OPENAI_API_KEY"] = "your-openai-api-key"
os.environ["SERPER_API_KEY"] = "your-serper-api-key"

# Initialize Mem0 client
client = MemoryClient()
```

## Store User Preferences

Set up initial conversation and preferences storage:

```python
def store_user_preferences(user_id: str, conversation: list):
    """Store user preferences from conversation history"""
    client.add(conversation, user_id=user_id)

# Example conversation storage
messages = [
    {
        "role": "user",
        "content": "Hi there! I'm planning a vacation and could use some advice.",
    },
    {
        "role": "assistant",
        "content": "Hello! I'd be happy to help with your vacation planning. What kind of destination do you prefer?",
    },
    {"role": "user", "content": "I am more of a beach person than a mountain person."},
    {
        "role": "assistant",
        "content": "That's interesting. Do you like hotels or airbnb?",
    },
    {"role": "user", "content": "I like airbnb more."},
]

store_user_preferences("crew_user_1", messages)
```

## Create CrewAI Agent

Define an agent with memory capabilities:

```python
def create_travel_agent():
    """Create a travel planning agent with search capabilities"""
    search_tool = SerperDevTool()

    return Agent(
        role="Personalized Travel Planner Agent",
        goal="Plan personalized travel itineraries",
        backstory="""You are a seasoned travel planner, known for your meticulous attention to detail.""",
        allow_delegation=False,
        memory=True,
        tools=[search_tool],
    )
```

## Define Tasks

Create tasks for your agent:

```python
def create_planning_task(agent, destination: str):
    """Create a travel planning task"""
    return Task(
        description=f"""Find places to live, eat, and visit in {destination}.""",
        expected_output=f"A detailed list of places to live, eat, and visit in {destination}.",
        agent=agent,
    )
```

## Set Up Crew

Configure the crew with memory integration:

```python
def setup_crew(agents: list, tasks: list):
    """Set up a crew with Mem0 memory integration"""
    return Crew(
        agents=agents,
        tasks=tasks,
        process=Process.sequential,
        memory=True,
        memory_config={
            "provider": "mem0",
            "config": {"user_id": "crew_user_1"},
        }
    )
```

## Main Execution Function

Implement the main function to run the travel planning system:

```python
def plan_trip(destination: str, user_id: str):
    # Create agent
    travel_agent = create_travel_agent()

    # Create task
    planning_task = create_planning_task(travel_agent, destination)

    # Setup crew
    crew = setup_crew([travel_agent], [planning_task])

    # Execute and return results
    return crew.kickoff()

# Example usage
if __name__ == "__main__":
    result = plan_trip("San Francisco", "crew_user_1")
    print(result)
```

## Key Features

1. **Persistent Memory**: Uses Mem0 to maintain user preferences and conversation history
2. **Agent-Based Architecture**: Leverages CrewAI's agent system for task execution
3. **Search Integration**: Includes SerperDev tool for real-world information retrieval
4. **Personalization**: Utilizes stored preferences for tailored recommendations

## Benefits

1. **Persistent Context & Memory**: Maintains user preferences and interaction history across sessions
2. **Flexible & Scalable Design**: Easily extendable with new agents, tasks and capabilities

## Conclusion

By combining CrewAI with Mem0, you can create sophisticated AI systems that maintain context and provide personalized experiences while leveraging the power of autonomous agents.

## Help

- For CrewAI documentation, visit [CrewAI Documentation](https://docs.crewai.com/)
- For Mem0 documentation, refer to the [Mem0 Platform](https://app.mem0.ai/)

<Snippet file="get-help.mdx" />
