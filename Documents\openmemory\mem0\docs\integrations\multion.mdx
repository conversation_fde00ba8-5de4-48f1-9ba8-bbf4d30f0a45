---
title: MultiOn
---

<Snippet file="paper-release.mdx" />

Build a personal browser agent that remembers user preferences and automates web tasks. It integrates Mem0 for memory management with MultiOn for executing browser actions, enabling personalized and efficient web interactions.

## Overview

In this guide, we'll explore two examples of creating Browser-based AI Agents:
1. An agent that searches [arxiv.org](https://arxiv.org) for research papers relevant to user's research interests.
2. A travel agent that provides personalized travel information based on user preferences. Refer to the [notebook](https://github.com/MULTI-ON/cookbook/blob/main/personalized-travel-agent/mem0_travel_agent.ipynb) for detailed code.

## Setup and Configuration

Install necessary libraries:

```bash
pip install mem0ai multion openai
```

First, we'll import the necessary libraries and set up our configurations.

```python
import os
from mem0 import Memory, MemoryClient
from multion.client import MultiOn
from openai import OpenAI

# Configuration
OPENAI_API_KEY = 'sk-xxx'  # Replace with your actual OpenAI API key
MULTION_API_KEY = 'your-multion-key'  # Replace with your actual MultiOn API key
MEM0_API_KEY = 'your-mem0-key'  # Replace with your actual Mem0 API key
USER_ID = "your-user-id"

# Set up OpenAI API key
os.environ['OPENAI_API_KEY'] = OPENAI_API_KEY
os.environ['MEM0_API_KEY'] = MEM0_API_KEY

# Initialize Mem0 and MultiOn
memory = Memory()  # For local usage
memory_client = MemoryClient()  # For API usage
multion = MultiOn(api_key=MULTION_API_KEY)
```

## Example 1: Research Paper Search Agent

### Add memories to Mem0

Define user data and add it to Mem0.

```python
USER_DATA = """
About me
- I'm Deshraj Yadav, Co-founder and CTO at Mem0, interested in AI and ML Infrastructure.
- Previously, I was a Senior Autopilot Engineer at Tesla, leading the AI Platform for Autopilot.
- I built EvalAI at Georgia Tech, an open-source platform for evaluating ML algorithms.
- Outside of work, I enjoy playing cricket in two leagues in the San Francisco.
"""

memory.add(USER_DATA, user_id=USER_ID)
print("User data added to memory.")
```

### Retrieving Relevant Memories

Define search command and retrieve relevant memories from Mem0.

```python
command = "Find papers on arxiv that I should read based on my interests."

relevant_memories = memory.search(command, user_id=USER_ID, limit=3)
relevant_memories_text = '\n'.join(mem['text'] for mem in relevant_memories)
print(f"Relevant memories:")
print(relevant_memories_text)
```

### Browsing arXiv

Use MultiOn to browse arXiv based on the command and relevant memories.

```python
prompt = f"{command}\n My past memories: {relevant_memories_text}"
browse_result = multion.browse(cmd=prompt, url="https://arxiv.org/")
print(browse_result)
```

## Example 2: Travel Agent

### Get Travel Information

Add conversation to Mem0 and create a function to get travel information based on user's question and optionally their preferences from memory.

<CodeGroup>
```python Code
def get_travel_info(question, use_memory=True):
    if use_memory:
        previous_memories = memory_client.search(question, user_id=USER_ID)
        relevant_memories_text = ""
        if previous_memories:
            print("Using previous memories to enhance the search...")
            relevant_memories_text = '\n'.join(mem["memory"] for mem in previous_memories)
        
        command = "Find travel information based on my interests:"
        prompt = f"{command}\n Question: {question} \n My preferences: {relevant_memories_text}"
    else:
        command = "Find travel information based on my interests:"
        prompt = f"{command}\n Question: {question}"
    
    print("Searching for travel information...")    
    browse_result = multion.browse(cmd=prompt)
    return browse_result.message

# Example usage
question = "Show me flight details for it."
answer_without_memory = get_travel_info(question, use_memory=False)
answer_with_memory = get_travel_info(question, use_memory=True)

print("Answer without memory:", answer_without_memory)
print("Answer with memory:", answer_with_memory)

# Another example
question = "What is the best place to eat there?"
answer_without_memory = get_travel_info(question, use_memory=False)
answer_with_memory = get_travel_info(question, use_memory=True)

print("Answer without memory:", answer_without_memory)
print("Answer with memory:", answer_with_memory)
```

```json Conversation
# Add conversation to Mem0
conversation = [
    {
        "role": "user",
        "content": "What are the best travel destinations in the world?"
    },
    {
        "role": "assistant",
        "content": "Could you please specify your interests or the type of travel information you are looking for? This will help me find the most relevant information for you."
    },
    {
        "role": "user",
        "content": "Sure, I want to travel to San Francisco."
    },
    {
        "role": "assistant",
        "content": """
        Based on the information gathered from TripAdvisor, here are some popular attractions, activities, and travel tips for San Francisco: \

        1. **Golden Gate Bridge**: A must-see iconic landmark. \
        2. **Alcatraz Island**: Famous former prison offering tours. \
        3. **Fisherman's Wharf**: Popular tourist area with shops, restaurants, and sea lions. \
        4. **Chinatown**: The largest Chinatown outside of Asia. \
        5. **Golden Gate Park**: Large urban park with gardens, museums, and recreational activities. \
        6. **Cable Cars**: Historic streetcars offering a unique way to see the city. \
        7. **Exploratorium**: Interactive science museum. \
        8. **San Francisco Museum of Modern Art (SFMOMA)**: Modern and contemporary art museum. \
        9. **Lombard Street**: Known for its steep, one-block section with eight hairpin turns. \
        10. **Union Square**: Major shopping and cultural hub. \

        Travel Tips: \
        - **Weather**: San Francisco has a mild climate, but it can be foggy and windy. Dress in layers. \
        - **Transportation**: Use public transportation like BART, Muni, and cable cars to get around. \
        - **Safety**: Be aware of your surroundings, especially in crowded tourist areas. \
        - **Dining**: Try local specialities like sourdough bread, seafood, and Mission-style burritos. \
        """
    },
    {
        "role": "user",
        "content": "Show me hotels around Golden Gate Bridge."
    },
    {
        "role": "assistant",
        "content": """The search results for hotels around Golden Gate Bridge in San Francisco include: \

        1. Hilton Hotels In San Francisco - Hotel Near Fishermans Wharf (hilton.com) \
        2. The 10 Closest Hotels to Golden Gate Bridge (tripadvisor.com) \
        3. Hotels near Golden Gate Bridge (expedia.com) \
        4. Hotels near Golden Gate Bridge (hotels.com) \
        5. Holiday Inn Express & Suites San Francisco Fishermans Wharf, an IHG Hotel $146 (1.8K) 3-star hotel Golden Gate Bridge • 3.5 mi DEAL 19% less than usual \
        6. Holiday Inn San Francisco-Golden Gateway, an IHG Hotel $151 (3.5K) 3-star hotel Golden Gate Bridge • 3.7 mi Casual hotel with dining, a bar & a pool \
        7. Hotel Zephyr San Francisco $159 (3.8K) 4-star hotel Golden Gate Bridge • 3.7 mi Nautical-themed lodging with bay views \
        8. Lodge at the Presidio \
        9. The Inn Above Tide \
        10. Cavallo Point \
        11. Casa Madrona Hotel and Spa \
        12. Cow Hollow Inn and Suites \
        13. Samesun San Francisco \
        14. Inn on Broadway \
        15. Coventry Motor Inn \
        16. HI San Francisco Fisherman's Wharf Hostel \
        17. Loews Regency San Francisco Hotel \
        18. Fairmont Heritage Place Ghirardelli Square \
        19. Hotel Drisco Pacific Heights \
        20. Travelodge by Wyndham Presidio San Francisco \
        """
    }
]
```
</CodeGroup>

## Conclusion

By integrating Mem0 with MultiOn, you've created personalized browser agents that remember user preferences and automate web tasks. The first example demonstrates a research-focused agent, while the second example shows a travel agent capable of providing personalized recommendations.

These examples illustrate how combining memory management with web browsing capabilities can create powerful, context-aware AI agents for various applications.

## Help

- For more details and advanced usage, refer to the full [cookbooks here](https://github.com/mem0ai/mem0/blob/main/cookbooks).
- Feel free to visit our [Github](https://github.com/mem0ai/mem0) or [Mem0 Platform](https://app.mem0.ai/).
- For any questions or assistance, please reach out to `taranjeetio` on [Discord](https://mem0.dev/DiD).

<Snippet file="get-help.mdx" />